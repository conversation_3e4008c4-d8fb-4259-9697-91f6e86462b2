# Web Development Ebook Cover

This is a professional ebook cover designed for "Web Development Beginner to Pro" with Amazon KDP specifications.

## Cover Specifications

- **Dimensions**: 2560 x 1600 pixels (Amazon KDP recommended)
- **Aspect Ratio**: 1.6:1 (perfect for Amazon KDP)
- **Format**: HTML/CSS (can be exported as image)
- **DPI**: Optimized for print quality

## Features

✨ **Professional Design Elements:**
- Modern gradient background with purple/blue theme
- Animated code snippets floating in background
- Tech icons representing web development
- Progress bar showing beginner to pro journey
- Elegant typography with Inter font
- Subtle animations and effects
- Responsive scaling for preview

🎨 **Design Highlights:**
- Eye-catching title with large, bold typography
- Professional color scheme that attracts buyers
- Clean, modern layout optimized for thumbnail visibility
- Tech-focused visual elements (HTML, React, CSS, etc.)
- Progress visualization showing skill development

## How to Use

1. **Preview**: Open `cover.html` in your browser to see the cover
2. **Customize**: Edit the HTML file to change:
   - Author name (currently "Your Name Here")
   - Colors in the CSS gradient
   - Tech icons or text
   - Background effects

3. **Export as Image**:
   - Use browser screenshot tools
   - Use online HTML to image converters
   - Use design tools like Figma to import and export
   - Recommended: Use a tool that can capture at full 2560x1600 resolution

## Customization Options

### Change Author Name
Replace "Your Name Here" in the HTML with your actual name.

### Modify Colors
The main gradient uses:
- Primary: `#667eea` to `#764ba2`
- You can change these hex codes to match your brand

### Tech Icons
Current icons: 🌐 ⚛️ 🚀 💻
You can replace with other relevant emojis or HTML entities.

### Background Code
Edit the floating code snippets to show different programming languages or frameworks.

## Amazon KDP Requirements Met

✅ **Resolution**: 2560 x 1600 pixels (high quality)
✅ **Aspect Ratio**: 1.6:1 (Amazon recommended)
✅ **Professional Design**: Clean, attractive layout
✅ **Readable Text**: Large, clear typography
✅ **Genre Appropriate**: Tech/programming theme
✅ **Thumbnail Friendly**: Visible at small sizes

## Export Instructions

1. Open `cover.html` in Chrome or Firefox
2. Use browser developer tools to set viewport to 2560x1600
3. Take a full-page screenshot or use tools like:
   - HTML/CSS to Image API
   - Puppeteer for programmatic capture
   - Online converters like htmlcsstoimage.com

## Tips for Amazon KDP

- Ensure text is readable at thumbnail size
- Use high contrast for better visibility
- Keep important elements away from edges
- Test how it looks in Amazon's search results
- Consider A/B testing different versions

The cover is designed to be professional, modern, and appealing to potential buyers interested in web development learning materials.
