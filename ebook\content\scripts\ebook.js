// Ebook Navigation and Functionality

// Current page tracking
let currentPage = 'cover';

// Show specific page
function showPage(pageId) {
    // Hide all pages
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });
    
    // Show selected page
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.add('active');
        currentPage = pageId;
        
        // Update navigation active state
        updateNavigation(pageId);
        
        // Scroll to top
        window.scrollTo(0, 0);
        
        // Update URL hash
        window.location.hash = pageId;
    }
}

// Update navigation active states
function updateNavigation(activePageId) {
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('onclick') && link.getAttribute('onclick').includes(activePageId)) {
            link.classList.add('active');
        }
    });
}

// Toggle mobile navigation
function toggleNav() {
    const navMenu = document.getElementById('navMenu');
    navMenu.classList.toggle('active');
}

// Initialize ebook
function initializeEbook() {
    // Check for hash in URL
    const hash = window.location.hash.substring(1);
    if (hash && document.getElementById(hash)) {
        showPage(hash);
    } else {
        showPage('cover');
    }
    
    // Add keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Add progress tracking
    initializeProgress();
    
    // Add reading time estimation
    addReadingTimeEstimates();
}

// Keyboard navigation
function handleKeyboardNavigation(event) {
    const pages = [
        'cover', 'table-of-contents', 'introduction', 
        'chapter1', 'chapter2', 'chapter3', 'chapter4', 
        'chapter5', 'chapter6', 'chapter7', 'chapter8', 
        'conclusion', 'resources'
    ];
    
    const currentIndex = pages.indexOf(currentPage);
    
    switch(event.key) {
        case 'ArrowRight':
        case 'PageDown':
            if (currentIndex < pages.length - 1) {
                showPage(pages[currentIndex + 1]);
            }
            break;
        case 'ArrowLeft':
        case 'PageUp':
            if (currentIndex > 0) {
                showPage(pages[currentIndex - 1]);
            }
            break;
        case 'Home':
            showPage('cover');
            break;
        case 'End':
            showPage('resources');
            break;
    }
}

// Progress tracking
function initializeProgress() {
    const totalPages = document.querySelectorAll('.page').length;
    let visitedPages = new Set();
    
    // Load visited pages from localStorage
    const saved = localStorage.getItem('ebook-progress');
    if (saved) {
        visitedPages = new Set(JSON.parse(saved));
    }
    
    // Track page visits
    const originalShowPage = showPage;
    showPage = function(pageId) {
        originalShowPage(pageId);
        visitedPages.add(pageId);
        localStorage.setItem('ebook-progress', JSON.stringify([...visitedPages]));
        updateProgressIndicator(visitedPages.size, totalPages);
    };
}

// Update progress indicator
function updateProgressIndicator(visited, total) {
    const percentage = Math.round((visited / total) * 100);
    
    // Create or update progress bar
    let progressBar = document.querySelector('.progress-indicator');
    if (!progressBar) {
        progressBar = document.createElement('div');
        progressBar.className = 'progress-indicator';
        progressBar.innerHTML = `
            <div class="progress-bar-container">
                <div class="progress-bar-fill"></div>
                <span class="progress-text">0%</span>
            </div>
        `;
        document.querySelector('.nav-header').appendChild(progressBar);
        
        // Add CSS for progress bar
        const style = document.createElement('style');
        style.textContent = `
            .progress-indicator {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }
            .progress-bar-container {
                position: relative;
                background: rgba(255, 255, 255, 0.1);
                height: 8px;
                border-radius: 4px;
                overflow: hidden;
            }
            .progress-bar-fill {
                height: 100%;
                background: linear-gradient(90deg, #4facfe, #00f2fe);
                border-radius: 4px;
                transition: width 0.3s ease;
                width: 0%;
            }
            .progress-text {
                position: absolute;
                top: -25px;
                right: 0;
                font-size: 0.8rem;
                color: rgba(255, 255, 255, 0.8);
            }
        `;
        document.head.appendChild(style);
    }
    
    const fill = progressBar.querySelector('.progress-bar-fill');
    const text = progressBar.querySelector('.progress-text');
    
    fill.style.width = percentage + '%';
    text.textContent = percentage + '%';
}

// Add reading time estimates
function addReadingTimeEstimates() {
    const pages = document.querySelectorAll('.page');
    const wordsPerMinute = 200; // Average reading speed
    
    pages.forEach(page => {
        const content = page.querySelector('.page-content');
        if (content) {
            const text = content.textContent || content.innerText;
            const wordCount = text.trim().split(/\s+/).length;
            const readingTime = Math.ceil(wordCount / wordsPerMinute);
            
            // Add reading time to page header
            const header = page.querySelector('.page-header');
            if (header && !header.querySelector('.reading-time')) {
                const timeElement = document.createElement('div');
                timeElement.className = 'reading-time';
                timeElement.innerHTML = `📖 ${readingTime} min read`;
                timeElement.style.cssText = `
                    color: #718096;
                    font-size: 0.9rem;
                    margin-top: 0.5rem;
                `;
                header.appendChild(timeElement);
            }
        }
    });
}

// Search functionality
function initializeSearch() {
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search ebook...';
    searchInput.className = 'search-input';
    searchInput.style.cssText = `
        width: 100%;
        padding: 0.5rem;
        margin: 1rem 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 0.9rem;
    `;
    
    searchInput.addEventListener('input', performSearch);
    document.querySelector('.nav-header').appendChild(searchInput);
}

// Perform search
function performSearch(event) {
    const query = event.target.value.toLowerCase();
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        const text = link.textContent.toLowerCase();
        const listItem = link.parentElement;
        
        if (text.includes(query) || query === '') {
            listItem.style.display = 'block';
        } else {
            listItem.style.display = 'none';
        }
    });
}

// Bookmark functionality
function toggleBookmark(pageId) {
    let bookmarks = JSON.parse(localStorage.getItem('ebook-bookmarks') || '[]');
    
    if (bookmarks.includes(pageId)) {
        bookmarks = bookmarks.filter(id => id !== pageId);
    } else {
        bookmarks.push(pageId);
    }
    
    localStorage.setItem('ebook-bookmarks', JSON.stringify(bookmarks));
    updateBookmarkUI(pageId, bookmarks.includes(pageId));
}

// Update bookmark UI
function updateBookmarkUI(pageId, isBookmarked) {
    const page = document.getElementById(pageId);
    const header = page.querySelector('.page-header');
    
    let bookmarkBtn = header.querySelector('.bookmark-btn');
    if (!bookmarkBtn) {
        bookmarkBtn = document.createElement('button');
        bookmarkBtn.className = 'bookmark-btn';
        bookmarkBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            float: right;
            margin-top: -2rem;
        `;
        bookmarkBtn.onclick = () => toggleBookmark(pageId);
        header.appendChild(bookmarkBtn);
    }
    
    bookmarkBtn.textContent = isBookmarked ? '🔖' : '📑';
    bookmarkBtn.title = isBookmarked ? 'Remove bookmark' : 'Add bookmark';
}

// Print functionality
function printPage() {
    window.print();
}

// Export functionality
function exportAsHTML() {
    const content = document.documentElement.outerHTML;
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'web-development-ebook.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEbook();
    initializeSearch();
    
    // Add bookmarks to existing pages
    const pages = ['introduction', 'chapter1', 'chapter2', 'chapter3', 'chapter4', 'chapter5', 'chapter6', 'chapter7', 'chapter8'];
    const bookmarks = JSON.parse(localStorage.getItem('ebook-bookmarks') || '[]');
    
    pages.forEach(pageId => {
        if (document.getElementById(pageId)) {
            updateBookmarkUI(pageId, bookmarks.includes(pageId));
        }
    });
});

// Handle browser back/forward buttons
window.addEventListener('popstate', function() {
    const hash = window.location.hash.substring(1);
    if (hash && document.getElementById(hash)) {
        showPage(hash);
    }
});

// Auto-save reading position
setInterval(() => {
    localStorage.setItem('ebook-last-page', currentPage);
}, 5000);

// Restore reading position on load
window.addEventListener('load', function() {
    const lastPage = localStorage.getItem('ebook-last-page');
    if (lastPage && document.getElementById(lastPage) && !window.location.hash) {
        showPage(lastPage);
    }
});
