<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Beginner to Pro - Ebook Cover</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .cover-container {
            width: 2560px;
            height: 1600px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.3);
            transform-origin: center;
        }

        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        }

        .code-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            font-family: 'Courier New', monospace;
            font-size: 24px;
            color: #fff;
            overflow: hidden;
        }

        .code-line {
            position: absolute;
            white-space: nowrap;
            animation: float 20s infinite linear;
        }

        .code-line:nth-child(1) { top: 10%; left: -100%; animation-delay: 0s; }
        .code-line:nth-child(2) { top: 25%; left: -100%; animation-delay: -5s; }
        .code-line:nth-child(3) { top: 40%; left: -100%; animation-delay: -10s; }
        .code-line:nth-child(4) { top: 55%; left: -100%; animation-delay: -15s; }
        .code-line:nth-child(5) { top: 70%; left: -100%; animation-delay: -7s; }
        .code-line:nth-child(6) { top: 85%; left: -100%; animation-delay: -12s; }

        @keyframes float {
            0% { transform: translateX(0); }
            100% { transform: translateX(calc(100vw + 200px)); }
        }

        .content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 120px 100px;
            text-align: center;
        }

        .title-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .main-title {
            font-size: 140px;
            font-weight: 900;
            color: #ffffff;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            margin-bottom: 40px;
            line-height: 1.1;
            letter-spacing: -2px;
        }

        .subtitle {
            font-size: 80px;
            font-weight: 600;
            color: #f0f0f0;
            margin-bottom: 60px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .tech-icons {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin: 80px 0;
        }

        .tech-icon {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: #fff;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .tech-icon:hover {
            transform: translateY(-10px);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 60px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 100%;
            border-radius: 10px;
            animation: progress 3s ease-in-out infinite alternate;
        }

        @keyframes progress {
            0% { width: 20%; }
            100% { width: 100%; }
        }

        .progress-labels {
            display: flex;
            justify-content: space-between;
            font-size: 32px;
            color: #fff;
            font-weight: 600;
            margin-top: 20px;
        }

        .author {
            font-size: 48px;
            font-weight: 400;
            color: #e0e0e0;
            margin-top: auto;
        }

        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
        }

        .circle-1 {
            width: 300px;
            height: 300px;
            top: 10%;
            right: 10%;
            animation: pulse 4s ease-in-out infinite;
        }

        .circle-2 {
            width: 200px;
            height: 200px;
            bottom: 20%;
            left: 15%;
            animation: pulse 4s ease-in-out infinite 2s;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.1); opacity: 0.1; }
        }

        /* Responsive scaling for preview */
        @media (max-width: 1920px) {
            .cover-container {
                transform: scale(0.25);
            }
        }

        @media (max-width: 1440px) {
            .cover-container {
                transform: scale(0.2);
            }
        }

        @media (max-width: 1024px) {
            .cover-container {
                transform: scale(0.15);
            }
        }
    </style>
</head>
<body>
    <div class="cover-container">
        <div class="background-pattern"></div>
        
        <div class="code-elements">
            <div class="code-line">&lt;html&gt; &lt;head&gt; &lt;title&gt;Web Development&lt;/title&gt; &lt;/head&gt;</div>
            <div class="code-line">function createWebsite() { return 'awesome'; }</div>
            <div class="code-line">.container { display: flex; justify-content: center; }</div>
            <div class="code-line">const express = require('express'); app.listen(3000);</div>
            <div class="code-line">&lt;div className="component"&gt; React Component &lt;/div&gt;</div>
            <div class="code-line">git commit -m "Building amazing websites"</div>
        </div>

        <div class="decorative-elements">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
        </div>

        <div class="content">
            <div class="title-section">
                <h1 class="main-title">WEB<br>DEVELOPMENT</h1>
                <h2 class="subtitle">BEGINNER TO PRO</h2>
                
                <div class="tech-icons">
                    <div class="tech-icon">🌐</div>
                    <div class="tech-icon">⚛️</div>
                    <div class="tech-icon">🚀</div>
                    <div class="tech-icon">💻</div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-labels">
                    <span>BEGINNER</span>
                    <span>INTERMEDIATE</span>
                    <span>PROFESSIONAL</span>
                </div>
            </div>

            <div class="author">
                Your Name Here
            </div>
        </div>
    </div>
</body>
</html>
