<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Beginner to Pro - Ebook Cover</title>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .cover-container {
            width: 2560px;
            height: 1600px;
            position: relative;
            background:
                radial-gradient(circle at 20% 20%, #ff6b6b 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, #4ecdc4 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, #45b7d1 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #f9ca24 0%, transparent 50%),
                radial-gradient(circle at 20% 80%, #6c5ce7 0%, transparent 50%),
                linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            overflow: hidden;
            box-shadow: 0 30px 100px rgba(0, 0, 0, 0.5);
            transform: scale(0.3);
            transform-origin: center;
        }

        .background-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(45deg, rgba(0, 0, 0, 0.3) 0%, transparent 50%, rgba(0, 0, 0, 0.2) 100%),
                radial-gradient(circle at center, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
        }

        .mesh-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                conic-gradient(from 0deg at 50% 50%,
                    rgba(255, 107, 107, 0.1) 0deg,
                    rgba(78, 205, 196, 0.1) 60deg,
                    rgba(69, 183, 209, 0.1) 120deg,
                    rgba(249, 202, 36, 0.1) 180deg,
                    rgba(108, 92, 231, 0.1) 240deg,
                    rgba(245, 87, 108, 0.1) 300deg,
                    rgba(255, 107, 107, 0.1) 360deg);
            filter: blur(100px);
        }

        .code-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.08;
            font-family: 'JetBrains Mono', monospace;
            font-size: 28px;
            color: #00ffff;
            overflow: hidden;
            font-weight: 500;
        }

        .code-line {
            position: absolute;
            white-space: nowrap;
        }

        .code-line:nth-child(1) {
            top: 15%;
            left: 5%;
            transform: rotate(-15deg);
        }
        .code-line:nth-child(2) {
            top: 30%;
            right: 10%;
            transform: rotate(10deg);
        }
        .code-line:nth-child(3) {
            top: 50%;
            left: 8%;
            transform: rotate(-8deg);
        }
        .code-line:nth-child(4) {
            top: 65%;
            right: 15%;
            transform: rotate(12deg);
        }
        .code-line:nth-child(5) {
            top: 80%;
            left: 12%;
            transform: rotate(-5deg);
        }

        .content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 120px 100px;
            text-align: center;
        }

        .header-section {
            text-align: center;
            margin-bottom: 80px;
        }

        .category {
            font-size: 48px;
            font-weight: 500;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 12px;
            margin-bottom: 40px;
            text-transform: uppercase;
            position: relative;
        }

        .category::before {
            content: '◆';
            position: absolute;
            left: -60px;
            top: 50%;
            transform: translateY(-50%);
            color: #ff6b6b;
            font-size: 32px;
        }

        .category::after {
            content: '◆';
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            color: #4ecdc4;
            font-size: 32px;
        }

        .main-title {
            font-size: 180px;
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 80px rgba(255, 255, 255, 0.3);
            margin-bottom: 30px;
            line-height: 0.85;
            letter-spacing: -6px;
            position: relative;
        }

        .main-title::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 6px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24);
            border-radius: 3px;
        }

        .subtitle {
            font-size: 84px;
            font-weight: 600;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #f9ca24 75%, #6c5ce7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 80px;
            letter-spacing: 3px;
            text-shadow: 0 0 40px rgba(255, 255, 255, 0.2);
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 60px;
            margin: 100px 0;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .tech-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .tech-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24);
        }

        .tech-icon {
            font-size: 60px;
            margin-bottom: 20px;
            display: block;
            color: #ffffff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .tech-label {
            font-size: 32px;
            color: #ffffff;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .tech-description {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        .html-icon { color: #e34c26; }
        .css-icon { color: #1572b6; }
        .js-icon { color: #f7df1e; }
        .react-icon { color: #61dafb; }

        .journey-section {
            margin: 80px 0;
        }

        .journey-title {
            font-size: 48px;
            color: #ffffff;
            font-weight: 700;
            margin-bottom: 40px;
        }

        .progress-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .progress-bar {
            width: 100%;
            height: 32px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            overflow: hidden;
            margin: 50px 0;
            border: 2px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                rgba(255, 107, 107, 0.1) 0%,
                rgba(78, 205, 196, 0.1) 33%,
                rgba(69, 183, 209, 0.1) 66%,
                rgba(249, 202, 36, 0.1) 100%);
            border-radius: 14px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 33%, #45b7d1 66%, #f9ca24 100%);
            width: 100%;
            border-radius: 14px;
            position: relative;
            z-index: 1;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
        }

        .progress-labels {
            display: flex;
            justify-content: space-between;
            font-size: 36px;
            color: #ffffff;
            font-weight: 700;
            margin-top: 30px;
        }

        .author-section {
            text-align: center;
            margin-top: auto;
        }

        .author {
            font-size: 52px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 20px;
        }

        .author-title {
            font-size: 32px;
            color: #00ffff;
            font-weight: 400;
        }

        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .geometric-shape {
            position: absolute;
            border: 2px solid rgba(0, 255, 255, 0.1);
        }

        .shape-1 {
            width: 200px;
            height: 200px;
            top: 8%;
            right: 8%;
            transform: rotate(45deg);
        }

        .shape-2 {
            width: 150px;
            height: 150px;
            bottom: 15%;
            left: 10%;
            border-radius: 50%;
        }

        .shape-3 {
            width: 100px;
            height: 100px;
            top: 25%;
            left: 5%;
            transform: rotate(30deg);
        }

        /* Responsive scaling for preview */
        @media (max-width: 1920px) {
            .cover-container {
                transform: scale(0.25);
            }
        }

        @media (max-width: 1440px) {
            .cover-container {
                transform: scale(0.2);
            }
        }

        @media (max-width: 1024px) {
            .cover-container {
                transform: scale(0.15);
            }
        }
    </style>
</head>
<body>
    <div class="cover-container">
        <div class="background-overlay"></div>
        <div class="mesh-gradient"></div>

        <div class="code-elements">
            <div class="code-line">&lt;!DOCTYPE html&gt;</div>
            <div class="code-line">const app = express();</div>
            <div class="code-line">.hero { background: linear-gradient(); }</div>
            <div class="code-line">import React from 'react';</div>
            <div class="code-line">function buildWebsite() { }</div>
        </div>

        <div class="decorative-elements">
            <div class="geometric-shape shape-1"></div>
            <div class="geometric-shape shape-2"></div>
            <div class="geometric-shape shape-3"></div>
        </div>

        <div class="content">
            <div class="header-section">
                <div class="category">Programming Guide</div>
                <h1 class="main-title">WEB<br>DEVELOPMENT</h1>
                <h2 class="subtitle">BEGINNER TO PRO</h2>
            </div>

            <div class="tech-stack">
                <div class="tech-card">
                    <i class="fab fa-html5 tech-icon html-icon"></i>
                    <div class="tech-label">HTML5</div>
                    <div class="tech-description">Structure</div>
                </div>
                <div class="tech-card">
                    <i class="fab fa-css3-alt tech-icon css-icon"></i>
                    <div class="tech-label">CSS3</div>
                    <div class="tech-description">Styling</div>
                </div>
                <div class="tech-card">
                    <i class="fab fa-js-square tech-icon js-icon"></i>
                    <div class="tech-label">JavaScript</div>
                    <div class="tech-description">Logic</div>
                </div>
                <div class="tech-card">
                    <i class="fab fa-react tech-icon react-icon"></i>
                    <div class="tech-label">React</div>
                    <div class="tech-description">Framework</div>
                </div>
            </div>

            <div class="journey-section">
                <div class="journey-title">Complete Learning Path</div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-labels">
                        <span>BEGINNER</span>
                        <span>INTERMEDIATE</span>
                        <span>EXPERT</span>
                    </div>
                </div>
            </div>

            <div class="author-section">
                <div class="author">Your Name Here</div>
                <div class="author-title">Full Stack Developer & Instructor</div>
            </div>
        </div>
    </div>
</body>
</html>
