<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Beginner to Pro - Ebook Cover</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .cover-container {
            width: 2560px;
            height: 1600px;
            position: relative;
            background: linear-gradient(145deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            overflow: hidden;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
            transform: scale(0.3);
            transform-origin: center;
        }

        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(30deg, rgba(0, 255, 255, 0.03) 12%, transparent 12.5%, transparent 87%, rgba(0, 255, 255, 0.03) 87.5%, rgba(0, 255, 255, 0.03)),
                linear-gradient(150deg, rgba(0, 255, 255, 0.03) 12%, transparent 12.5%, transparent 87%, rgba(0, 255, 255, 0.03) 87.5%, rgba(0, 255, 255, 0.03)),
                linear-gradient(30deg, rgba(255, 255, 255, 0.02) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.02) 87.5%, rgba(255, 255, 255, 0.02)),
                linear-gradient(150deg, rgba(255, 255, 255, 0.02) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.02) 87.5%, rgba(255, 255, 255, 0.02));
            background-size: 80px 80px;
        }

        .code-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.08;
            font-family: 'JetBrains Mono', monospace;
            font-size: 28px;
            color: #00ffff;
            overflow: hidden;
            font-weight: 500;
        }

        .code-line {
            position: absolute;
            white-space: nowrap;
        }

        .code-line:nth-child(1) {
            top: 15%;
            left: 5%;
            transform: rotate(-15deg);
        }
        .code-line:nth-child(2) {
            top: 30%;
            right: 10%;
            transform: rotate(10deg);
        }
        .code-line:nth-child(3) {
            top: 50%;
            left: 8%;
            transform: rotate(-8deg);
        }
        .code-line:nth-child(4) {
            top: 65%;
            right: 15%;
            transform: rotate(12deg);
        }
        .code-line:nth-child(5) {
            top: 80%;
            left: 12%;
            transform: rotate(-5deg);
        }

        .content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 120px 100px;
            text-align: center;
        }

        .header-section {
            text-align: center;
            margin-bottom: 80px;
        }

        .category {
            font-size: 42px;
            font-weight: 600;
            color: #00ffff;
            letter-spacing: 8px;
            margin-bottom: 30px;
            text-transform: uppercase;
        }

        .main-title {
            font-size: 160px;
            font-weight: 900;
            color: #ffffff;
            text-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            line-height: 0.9;
            letter-spacing: -4px;
        }

        .subtitle {
            font-size: 72px;
            font-weight: 700;
            background: linear-gradient(135deg, #00ffff 0%, #ff6b6b 50%, #4ecdc4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 80px;
            letter-spacing: 2px;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 80px;
            margin: 100px 0;
        }

        .tech-item {
            text-align: center;
        }

        .tech-icon {
            width: 140px;
            height: 140px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 70px;
            color: #00ffff;
            border: 3px solid rgba(0, 255, 255, 0.3);
            margin-bottom: 20px;
        }

        .tech-label {
            font-size: 28px;
            color: #ffffff;
            font-weight: 600;
        }

        .journey-section {
            margin: 80px 0;
        }

        .journey-title {
            font-size: 48px;
            color: #ffffff;
            font-weight: 700;
            margin-bottom: 40px;
        }

        .progress-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            overflow: hidden;
            margin: 40px 0;
            border: 2px solid rgba(0, 255, 255, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 50%, #00ffff 100%);
            width: 100%;
            border-radius: 10px;
        }

        .progress-labels {
            display: flex;
            justify-content: space-between;
            font-size: 36px;
            color: #ffffff;
            font-weight: 700;
            margin-top: 30px;
        }

        .author-section {
            text-align: center;
            margin-top: auto;
        }

        .author {
            font-size: 52px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 20px;
        }

        .author-title {
            font-size: 32px;
            color: #00ffff;
            font-weight: 400;
        }

        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .geometric-shape {
            position: absolute;
            border: 2px solid rgba(0, 255, 255, 0.1);
        }

        .shape-1 {
            width: 200px;
            height: 200px;
            top: 8%;
            right: 8%;
            transform: rotate(45deg);
        }

        .shape-2 {
            width: 150px;
            height: 150px;
            bottom: 15%;
            left: 10%;
            border-radius: 50%;
        }

        .shape-3 {
            width: 100px;
            height: 100px;
            top: 25%;
            left: 5%;
            transform: rotate(30deg);
        }

        /* Responsive scaling for preview */
        @media (max-width: 1920px) {
            .cover-container {
                transform: scale(0.25);
            }
        }

        @media (max-width: 1440px) {
            .cover-container {
                transform: scale(0.2);
            }
        }

        @media (max-width: 1024px) {
            .cover-container {
                transform: scale(0.15);
            }
        }
    </style>
</head>
<body>
    <div class="cover-container">
        <div class="background-pattern"></div>

        <div class="code-elements">
            <div class="code-line">&lt;!DOCTYPE html&gt;</div>
            <div class="code-line">const app = express();</div>
            <div class="code-line">.hero { background: linear-gradient(); }</div>
            <div class="code-line">import React from 'react';</div>
            <div class="code-line">function buildWebsite() { }</div>
        </div>

        <div class="decorative-elements">
            <div class="geometric-shape shape-1"></div>
            <div class="geometric-shape shape-2"></div>
            <div class="geometric-shape shape-3"></div>
        </div>

        <div class="content">
            <div class="header-section">
                <div class="category">Programming Guide</div>
                <h1 class="main-title">WEB<br>DEVELOPMENT</h1>
                <h2 class="subtitle">BEGINNER TO PRO</h2>
            </div>

            <div class="tech-stack">
                <div class="tech-item">
                    <div class="tech-icon">{ }</div>
                    <div class="tech-label">JavaScript</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">⚛</div>
                    <div class="tech-label">React</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🎨</div>
                    <div class="tech-label">CSS</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🚀</div>
                    <div class="tech-label">Node.js</div>
                </div>
            </div>

            <div class="journey-section">
                <div class="journey-title">Complete Learning Path</div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-labels">
                        <span>BEGINNER</span>
                        <span>INTERMEDIATE</span>
                        <span>EXPERT</span>
                    </div>
                </div>
            </div>

            <div class="author-section">
                <div class="author">Your Name Here</div>
                <div class="author-title">Full Stack Developer & Instructor</div>
            </div>
        </div>
    </div>
</body>
</html>
