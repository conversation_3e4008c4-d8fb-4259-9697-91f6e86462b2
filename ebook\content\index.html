<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development: Beginner to Pro - Complete Guide</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/ebook.css">
</head>
<body>
    <div class="ebook-container">
        <!-- Navigation -->
        <nav class="ebook-nav">
            <div class="nav-header">
                <h1>Web Development: Beginner to Pro</h1>
                <button class="nav-toggle" onclick="toggleNav()">☰</button>
            </div>
            <ul class="nav-menu" id="navMenu">
                <li><a href="#cover" onclick="showPage('cover')">Cover</a></li>
                <li><a href="#table-of-contents" onclick="showPage('table-of-contents')">Table of Contents</a></li>
                <li><a href="#introduction" onclick="showPage('introduction')">Introduction</a></li>
                <li><a href="#chapter1" onclick="showPage('chapter1')">Chapter 1: HTML Fundamentals</a></li>
                <li><a href="#chapter2" onclick="showPage('chapter2')">Chapter 2: CSS Mastery</a></li>
                <li><a href="#chapter3" onclick="showPage('chapter3')">Chapter 3: JavaScript Essentials</a></li>
                <li><a href="#chapter4" onclick="showPage('chapter4')">Chapter 4: React Development</a></li>
                <li><a href="#chapter5" onclick="showPage('chapter5')">Chapter 5: Backend with Node.js</a></li>
                <li><a href="#chapter6" onclick="showPage('chapter6')">Chapter 6: Database Integration</a></li>
                <li><a href="#chapter7" onclick="showPage('chapter7')">Chapter 7: Deployment & DevOps</a></li>
                <li><a href="#chapter8" onclick="showPage('chapter8')">Chapter 8: Advanced Topics</a></li>
                <li><a href="#conclusion" onclick="showPage('conclusion')">Conclusion</a></li>
                <li><a href="#resources" onclick="showPage('resources')">Resources</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="ebook-content">
            <!-- Cover Page -->
            <div id="cover" class="page active">
                <div class="cover-page">
                    <div class="cover-header">
                        <span class="cover-category">Complete Programming Guide</span>
                        <h1 class="cover-title">WEB DEVELOPMENT</h1>
                        <h2 class="cover-subtitle">BEGINNER TO PRO</h2>
                    </div>
                    
                    <div class="cover-features">
                        <div class="feature-grid">
                            <div class="feature-item">
                                <div class="feature-icon">🌐</div>
                                <h3>Frontend Development</h3>
                                <p>HTML, CSS, JavaScript, React</p>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">⚙️</div>
                                <h3>Backend Development</h3>
                                <p>Node.js, Express, APIs</p>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">🗄️</div>
                                <h3>Database Management</h3>
                                <p>SQL, NoSQL, MongoDB</p>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">🚀</div>
                                <h3>Deployment & DevOps</h3>
                                <p>Cloud hosting, CI/CD</p>
                            </div>
                        </div>
                    </div>

                    <div class="cover-footer">
                        <div class="author-info">
                            <h3>Your Name Here</h3>
                            <p>Full Stack Developer & Instructor</p>
                        </div>
                        <div class="book-stats">
                            <span>8 Chapters • 200+ Pages • 50+ Code Examples</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table of Contents -->
            <div id="table-of-contents" class="page">
                <div class="page-header">
                    <h1>Table of Contents</h1>
                </div>
                <div class="toc-content">
                    <div class="toc-section">
                        <h2>Getting Started</h2>
                        <ul class="toc-list">
                            <li><a href="#introduction" onclick="showPage('introduction')">Introduction</a> <span class="page-num">3</span></li>
                            <li><a href="#setup" onclick="showPage('setup')">Development Environment Setup</a> <span class="page-num">8</span></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h2>Frontend Development</h2>
                        <ul class="toc-list">
                            <li><a href="#chapter1" onclick="showPage('chapter1')">Chapter 1: HTML Fundamentals</a> <span class="page-num">15</span></li>
                            <li><a href="#chapter2" onclick="showPage('chapter2')">Chapter 2: CSS Mastery</a> <span class="page-num">35</span></li>
                            <li><a href="#chapter3" onclick="showPage('chapter3')">Chapter 3: JavaScript Essentials</a> <span class="page-num">65</span></li>
                            <li><a href="#chapter4" onclick="showPage('chapter4')">Chapter 4: React Development</a> <span class="page-num">95</span></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h2>Backend Development</h2>
                        <ul class="toc-list">
                            <li><a href="#chapter5" onclick="showPage('chapter5')">Chapter 5: Backend with Node.js</a> <span class="page-num">125</span></li>
                            <li><a href="#chapter6" onclick="showPage('chapter6')">Chapter 6: Database Integration</a> <span class="page-num">155</span></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h2>Advanced Topics</h2>
                        <ul class="toc-list">
                            <li><a href="#chapter7" onclick="showPage('chapter7')">Chapter 7: Deployment & DevOps</a> <span class="page-num">175</span></li>
                            <li><a href="#chapter8" onclick="showPage('chapter8')">Chapter 8: Advanced Concepts</a> <span class="page-num">195</span></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h2>Conclusion</h2>
                        <ul class="toc-list">
                            <li><a href="#conclusion" onclick="showPage('conclusion')">Final Thoughts & Next Steps</a> <span class="page-num">215</span></li>
                            <li><a href="#resources" onclick="showPage('resources')">Additional Resources</a> <span class="page-num">220</span></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Introduction -->
            <div id="introduction" class="page">
                <div class="page-header">
                    <span class="chapter-label">Introduction</span>
                    <h1>Welcome to Web Development</h1>
                </div>
                <div class="page-content">
                    <div class="intro-section">
                        <h2>Why This Book?</h2>
                        <p>Web development is one of the most in-demand skills in today's digital world. Whether you're looking to start a new career, build your own projects, or enhance your existing skills, this comprehensive guide will take you from complete beginner to professional developer.</p>

                        <div class="highlight-box">
                            <h3>🎯 What You'll Learn</h3>
                            <ul>
                                <li>Build responsive, modern websites from scratch</li>
                                <li>Master HTML, CSS, and JavaScript fundamentals</li>
                                <li>Create dynamic applications with React</li>
                                <li>Develop backend APIs with Node.js</li>
                                <li>Work with databases and data management</li>
                                <li>Deploy applications to production</li>
                                <li>Follow industry best practices and standards</li>
                            </ul>
                        </div>

                        <h2>Who This Book Is For</h2>
                        <div class="audience-grid">
                            <div class="audience-card">
                                <h3>🔰 Complete Beginners</h3>
                                <p>No prior programming experience required. We start from the very basics and build up your knowledge step by step.</p>
                            </div>
                            <div class="audience-card">
                                <h3>💼 Career Changers</h3>
                                <p>Looking to transition into tech? This book provides the practical skills needed for web development roles.</p>
                            </div>
                            <div class="audience-card">
                                <h3>🎓 Students & Self-Learners</h3>
                                <p>Supplement your formal education with hands-on, practical web development skills.</p>
                            </div>
                            <div class="audience-card">
                                <h3>🚀 Entrepreneurs</h3>
                                <p>Build your own web applications and bring your ideas to life without relying on others.</p>
                            </div>
                        </div>

                        <h2>How to Use This Book</h2>
                        <div class="learning-path">
                            <div class="path-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h3>Read & Understand</h3>
                                    <p>Each chapter builds on the previous one. Read through the concepts and explanations carefully.</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h3>Practice & Code</h3>
                                    <p>Follow along with the code examples. Type them out yourself - don't just copy and paste.</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h3>Build Projects</h3>
                                    <p>Complete the projects at the end of each chapter to reinforce your learning.</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h3>Experiment & Explore</h3>
                                    <p>Modify the examples, try new approaches, and build your own variations.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page-footer">
                    <button class="nav-btn prev" onclick="showPage('table-of-contents')">← Table of Contents</button>
                    <button class="nav-btn next" onclick="showPage('chapter1')">Chapter 1: HTML Fundamentals →</button>
                </div>
            </div>

            <!-- Chapter 1: HTML Fundamentals -->
            <div id="chapter1" class="page">
                <div class="page-header">
                    <span class="chapter-label">Chapter 1</span>
                    <h1>HTML Fundamentals</h1>
                </div>
                <div class="page-content">
                    <h2>What is HTML?</h2>
                    <p>HTML (HyperText Markup Language) is the foundation of every website. It's a markup language that tells the browser how to structure and display content. Think of HTML as the skeleton of a webpage - it provides the basic structure that everything else builds upon.</p>

                    <div class="highlight-box">
                        <h3>🏗️ Key Concepts</h3>
                        <ul>
                            <li><strong>Elements:</strong> Building blocks of HTML (like paragraphs, headings, images)</li>
                            <li><strong>Tags:</strong> Keywords surrounded by angle brackets that define elements</li>
                            <li><strong>Attributes:</strong> Additional information about elements</li>
                            <li><strong>Semantic HTML:</strong> Using meaningful tags that describe content</li>
                        </ul>
                    </div>

                    <h2>Basic HTML Structure</h2>
                    <p>Every HTML document follows a standard structure. Here's the basic template:</p>

                    <div class="code-example">
                        <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;My First Webpage&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;Welcome to My Website&lt;/h1&gt;
    &lt;p&gt;This is my first paragraph.&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                    </div>

                    <h3>Breaking Down the Structure</h3>
                    <ul>
                        <li><code>&lt;!DOCTYPE html&gt;</code> - Tells the browser this is HTML5</li>
                        <li><code>&lt;html&gt;</code> - Root element that contains all content</li>
                        <li><code>&lt;head&gt;</code> - Contains metadata (not visible on page)</li>
                        <li><code>&lt;body&gt;</code> - Contains all visible content</li>
                    </ul>

                    <h2>Essential HTML Elements</h2>

                    <h3>Headings</h3>
                    <p>HTML provides six levels of headings, from h1 (most important) to h6 (least important):</p>

                    <div class="code-example">
                        <pre><code>&lt;h1&gt;Main Title&lt;/h1&gt;
&lt;h2&gt;Section Title&lt;/h2&gt;
&lt;h3&gt;Subsection Title&lt;/h3&gt;
&lt;h4&gt;Sub-subsection Title&lt;/h4&gt;
&lt;h5&gt;Minor Heading&lt;/h5&gt;
&lt;h6&gt;Smallest Heading&lt;/h6&gt;</code></pre>
                    </div>

                    <h3>Paragraphs and Text</h3>
                    <div class="code-example">
                        <pre><code>&lt;p&gt;This is a paragraph of text.&lt;/p&gt;
&lt;p&gt;This is another paragraph with &lt;strong&gt;bold text&lt;/strong&gt; and &lt;em&gt;italic text&lt;/em&gt;.&lt;/p&gt;
&lt;br&gt; &lt;!-- Line break --&gt;
&lt;hr&gt; &lt;!-- Horizontal rule --&gt;</code></pre>
                    </div>

                    <h3>Lists</h3>
                    <div class="code-example">
                        <pre><code>&lt;!-- Unordered List --&gt;
&lt;ul&gt;
    &lt;li&gt;First item&lt;/li&gt;
    &lt;li&gt;Second item&lt;/li&gt;
    &lt;li&gt;Third item&lt;/li&gt;
&lt;/ul&gt;

&lt;!-- Ordered List --&gt;
&lt;ol&gt;
    &lt;li&gt;Step one&lt;/li&gt;
    &lt;li&gt;Step two&lt;/li&gt;
    &lt;li&gt;Step three&lt;/li&gt;
&lt;/ol&gt;</code></pre>
                    </div>

                    <h3>Links and Images</h3>
                    <div class="code-example">
                        <pre><code>&lt;!-- Links --&gt;
&lt;a href="https://www.example.com"&gt;Visit Example.com&lt;/a&gt;
&lt;a href="about.html"&gt;About Page&lt;/a&gt;
&lt;a href="mailto:<EMAIL>"&gt;Send Email&lt;/a&gt;

&lt;!-- Images --&gt;
&lt;img src="image.jpg" alt="Description of image"&gt;
&lt;img src="https://example.com/photo.png" alt="Remote image"&gt;</code></pre>
                    </div>

                    <h2>Semantic HTML</h2>
                    <p>Semantic HTML uses elements that clearly describe their meaning and purpose. This improves accessibility and SEO:</p>

                    <div class="code-example">
                        <pre><code>&lt;header&gt;
    &lt;nav&gt;
        &lt;ul&gt;
            &lt;li&gt;&lt;a href="/"&gt;Home&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="/about"&gt;About&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="/contact"&gt;Contact&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/nav&gt;
&lt;/header&gt;

&lt;main&gt;
    &lt;article&gt;
        &lt;h1&gt;Article Title&lt;/h1&gt;
        &lt;p&gt;Article content goes here...&lt;/p&gt;
    &lt;/article&gt;

    &lt;aside&gt;
        &lt;h2&gt;Related Links&lt;/h2&gt;
        &lt;ul&gt;
            &lt;li&gt;&lt;a href="#"&gt;Related Article 1&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="#"&gt;Related Article 2&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/aside&gt;
&lt;/main&gt;

&lt;footer&gt;
    &lt;p&gt;&copy; 2024 My Website. All rights reserved.&lt;/p&gt;
&lt;/footer&gt;</code></pre>
                    </div>

                    <h2>Practice Project: Personal Portfolio Page</h2>
                    <p>Let's create a simple personal portfolio page using the HTML elements we've learned:</p>

                    <div class="project-box">
                        <h3>🎯 Project Goals</h3>
                        <ul>
                            <li>Create a complete HTML page structure</li>
                            <li>Use semantic HTML elements</li>
                            <li>Include headings, paragraphs, lists, and links</li>
                            <li>Add an image and proper alt text</li>
                        </ul>
                    </div>

                    <div class="code-example">
                        <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;John Doe - Web Developer&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;header&gt;
        &lt;h1&gt;John Doe&lt;/h1&gt;
        &lt;p&gt;Web Developer &amp; Designer&lt;/p&gt;
    &lt;/header&gt;

    &lt;nav&gt;
        &lt;ul&gt;
            &lt;li&gt;&lt;a href="#about"&gt;About&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="#skills"&gt;Skills&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="#projects"&gt;Projects&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="#contact"&gt;Contact&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/nav&gt;

    &lt;main&gt;
        &lt;section id="about"&gt;
            &lt;h2&gt;About Me&lt;/h2&gt;
            &lt;img src="profile.jpg" alt="John Doe profile photo"&gt;
            &lt;p&gt;I'm a passionate web developer with 3 years of experience creating beautiful, functional websites. I love turning ideas into reality through code.&lt;/p&gt;
        &lt;/section&gt;

        &lt;section id="skills"&gt;
            &lt;h2&gt;Skills&lt;/h2&gt;
            &lt;ul&gt;
                &lt;li&gt;HTML5 &amp; CSS3&lt;/li&gt;
                &lt;li&gt;JavaScript&lt;/li&gt;
                &lt;li&gt;React&lt;/li&gt;
                &lt;li&gt;Node.js&lt;/li&gt;
            &lt;/ul&gt;
        &lt;/section&gt;

        &lt;section id="projects"&gt;
            &lt;h2&gt;Projects&lt;/h2&gt;
            &lt;article&gt;
                &lt;h3&gt;E-commerce Website&lt;/h3&gt;
                &lt;p&gt;A full-stack e-commerce solution built with React and Node.js.&lt;/p&gt;
                &lt;a href="https://github.com/johndoe/ecommerce"&gt;View on GitHub&lt;/a&gt;
            &lt;/article&gt;
        &lt;/section&gt;
    &lt;/main&gt;

    &lt;footer&gt;
        &lt;section id="contact"&gt;
            &lt;h2&gt;Contact Me&lt;/h2&gt;
            &lt;p&gt;Email: &lt;a href="mailto:<EMAIL>"&gt;<EMAIL>&lt;/a&gt;&lt;/p&gt;
            &lt;p&gt;LinkedIn: &lt;a href="https://linkedin.com/in/johndoe"&gt;linkedin.com/in/johndoe&lt;/a&gt;&lt;/p&gt;
        &lt;/section&gt;
    &lt;/footer&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                    </div>

                    <h2>Key Takeaways</h2>
                    <div class="highlight-box">
                        <h3>✅ What You've Learned</h3>
                        <ul>
                            <li>HTML is the structural foundation of web pages</li>
                            <li>Every HTML document has a standard structure</li>
                            <li>Semantic elements improve accessibility and SEO</li>
                            <li>Proper nesting and indentation make code readable</li>
                            <li>Always include alt text for images</li>
                            <li>Use meaningful, descriptive element names</li>
                        </ul>
                    </div>

                    <h2>Next Steps</h2>
                    <p>Now that you understand HTML fundamentals, you're ready to learn CSS to style your web pages and make them visually appealing. In the next chapter, we'll explore how to add colors, fonts, layouts, and responsive design to your HTML structure.</p>
                </div>
                <div class="page-footer">
                    <button class="nav-btn prev" onclick="showPage('introduction')">← Introduction</button>
                    <button class="nav-btn next" onclick="showPage('chapter2')">Chapter 2: CSS Mastery →</button>
                </div>
            </div>

            <!-- Chapter 2: CSS Mastery -->
            <div id="chapter2" class="page">
                <div class="page-header">
                    <span class="chapter-label">Chapter 2</span>
                    <h1>CSS Mastery</h1>
                </div>
                <div class="page-content">
                    <h2>What is CSS?</h2>
                    <p>CSS (Cascading Style Sheets) is the language used to style and layout web pages. While HTML provides the structure, CSS makes your websites beautiful, responsive, and user-friendly. It controls colors, fonts, spacing, layouts, and animations.</p>

                    <div class="highlight-box">
                        <h3>🎨 CSS Powers</h3>
                        <ul>
                            <li><strong>Visual Design:</strong> Colors, fonts, shadows, gradients</li>
                            <li><strong>Layout:</strong> Positioning, flexbox, grid systems</li>
                            <li><strong>Responsive Design:</strong> Mobile-first, adaptive layouts</li>
                            <li><strong>Animations:</strong> Transitions, keyframes, transforms</li>
                            <li><strong>User Experience:</strong> Hover effects, interactive elements</li>
                        </ul>
                    </div>

                    <h2>CSS Syntax and Selectors</h2>
                    <p>CSS follows a simple syntax pattern: selector { property: value; }</p>

                    <div class="code-example">
                        <pre><code>/* Basic syntax */
h1 {
    color: blue;
    font-size: 2rem;
    text-align: center;
}

/* Class selector */
.highlight {
    background-color: yellow;
    padding: 10px;
}

/* ID selector */
#header {
    background-color: #333;
    color: white;
}

/* Element with class */
p.intro {
    font-size: 1.2rem;
    font-weight: bold;
}</code></pre>
                    </div>

                    <h3>Common Selectors</h3>
                    <div class="code-example">
                        <pre><code>/* Universal selector */
* {
    margin: 0;
    padding: 0;
}

/* Descendant selector */
nav ul li {
    list-style: none;
}

/* Child selector */
nav > ul {
    display: flex;
}

/* Pseudo-classes */
a:hover {
    color: red;
}

button:active {
    transform: scale(0.95);
}

/* Pseudo-elements */
p::first-line {
    font-weight: bold;
}

h1::before {
    content: "★ ";
}</code></pre>
                    </div>

                    <h2>The Box Model</h2>
                    <p>Understanding the CSS box model is crucial for layout control. Every element is a rectangular box with four areas:</p>

                    <div class="info-box">
                        <h3>📦 Box Model Components</h3>
                        <ul>
                            <li><strong>Content:</strong> The actual content (text, images)</li>
                            <li><strong>Padding:</strong> Space between content and border</li>
                            <li><strong>Border:</strong> Line around the padding</li>
                            <li><strong>Margin:</strong> Space outside the border</li>
                        </ul>
                    </div>

                    <div class="code-example">
                        <pre><code>.box {
    /* Content area */
    width: 300px;
    height: 200px;

    /* Padding - inside spacing */
    padding: 20px;
    /* padding: 10px 20px; top/bottom left/right */
    /* padding: 10px 15px 20px 25px; top right bottom left */

    /* Border */
    border: 2px solid #333;
    border-radius: 10px;

    /* Margin - outside spacing */
    margin: 30px auto; /* auto centers horizontally */

    /* Box-sizing controls how width/height are calculated */
    box-sizing: border-box; /* includes padding and border in width */
}</code></pre>
                    </div>

                    <h2>Colors and Typography</h2>

                    <h3>Working with Colors</h3>
                    <div class="code-example">
                        <pre><code>/* Color formats */
.color-examples {
    /* Named colors */
    color: red;
    background-color: lightblue;

    /* Hexadecimal */
    color: #ff6b6b;
    background-color: #4ecdc4;

    /* RGB */
    color: rgb(255, 107, 107);
    background-color: rgb(78, 205, 196);

    /* RGBA (with transparency) */
    color: rgba(255, 107, 107, 0.8);
    background-color: rgba(78, 205, 196, 0.5);

    /* HSL (Hue, Saturation, Lightness) */
    color: hsl(0, 100%, 71%);
    background-color: hsl(174, 57%, 55%);
}</code></pre>
                    </div>

                    <h3>Typography Control</h3>
                    <div class="code-example">
                        <pre><code>/* Font properties */
.typography {
    /* Font family with fallbacks */
    font-family: 'Helvetica Neue', Arial, sans-serif;

    /* Font size */
    font-size: 1.2rem; /* relative unit */
    font-size: 18px;   /* absolute unit */

    /* Font weight */
    font-weight: 400;  /* normal */
    font-weight: 700;  /* bold */
    font-weight: bold;

    /* Font style */
    font-style: italic;

    /* Line height for readability */
    line-height: 1.6;

    /* Letter and word spacing */
    letter-spacing: 0.5px;
    word-spacing: 2px;

    /* Text alignment */
    text-align: center;
    text-align: justify;

    /* Text decoration */
    text-decoration: underline;
    text-decoration: none; /* remove underlines from links */

    /* Text transform */
    text-transform: uppercase;
    text-transform: capitalize;
}</code></pre>
                    </div>

                    <h2>Layout with Flexbox</h2>
                    <p>Flexbox is a powerful layout method for arranging elements in rows or columns:</p>

                    <div class="code-example">
                        <pre><code>/* Flex container */
.flex-container {
    display: flex;

    /* Direction */
    flex-direction: row;        /* default */
    flex-direction: column;
    flex-direction: row-reverse;

    /* Justify content (main axis) */
    justify-content: flex-start;  /* default */
    justify-content: center;
    justify-content: space-between;
    justify-content: space-around;
    justify-content: space-evenly;

    /* Align items (cross axis) */
    align-items: stretch;      /* default */
    align-items: center;
    align-items: flex-start;
    align-items: flex-end;

    /* Wrap items */
    flex-wrap: nowrap;         /* default */
    flex-wrap: wrap;

    /* Gap between items */
    gap: 20px;
}

/* Flex items */
.flex-item {
    /* Grow factor */
    flex-grow: 1;    /* take up available space */

    /* Shrink factor */
    flex-shrink: 0;  /* don't shrink */

    /* Base size */
    flex-basis: 200px;

    /* Shorthand */
    flex: 1 0 200px; /* grow shrink basis */
}</code></pre>
                    </div>

                    <h2>CSS Grid Layout</h2>
                    <p>CSS Grid provides two-dimensional layout control:</p>

                    <div class="code-example">
                        <pre><code>/* Grid container */
.grid-container {
    display: grid;

    /* Define columns */
    grid-template-columns: 1fr 2fr 1fr;           /* fractional units */
    grid-template-columns: 200px 1fr 100px;      /* mixed units */
    grid-template-columns: repeat(3, 1fr);       /* repeat function */
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

    /* Define rows */
    grid-template-rows: 100px auto 50px;

    /* Gap between grid items */
    gap: 20px;
    grid-gap: 20px; /* older syntax */

    /* Named grid areas */
    grid-template-areas:
        "header header header"
        "sidebar main main"
        "footer footer footer";
}

/* Grid items */
.grid-item {
    /* Position by line numbers */
    grid-column: 1 / 3;    /* span from line 1 to 3 */
    grid-row: 2 / 4;

    /* Position by area name */
    grid-area: header;

    /* Shorthand */
    grid-area: 2 / 1 / 4 / 3; /* row-start / col-start / row-end / col-end */
}</code></pre>
                    </div>

                    <h2>Responsive Design</h2>
                    <p>Make your websites work on all devices with responsive design techniques:</p>

                    <div class="code-example">
                        <pre><code>/* Mobile-first approach */
.responsive-container {
    /* Base styles for mobile */
    padding: 1rem;
    font-size: 1rem;
}

/* Tablet styles */
@media (min-width: 768px) {
    .responsive-container {
        padding: 2rem;
        font-size: 1.1rem;
    }
}

/* Desktop styles */
@media (min-width: 1024px) {
    .responsive-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 3rem;
        font-size: 1.2rem;
    }
}

/* Common breakpoints */
@media (max-width: 480px)  { /* Mobile */ }
@media (min-width: 481px) and (max-width: 768px) { /* Tablet */ }
@media (min-width: 769px) and (max-width: 1024px) { /* Small desktop */ }
@media (min-width: 1025px) { /* Large desktop */ }</code></pre>
                    </div>

                    <h2>CSS Animations and Transitions</h2>

                    <h3>Transitions</h3>
                    <div class="code-example">
                        <pre><code>/* Smooth transitions */
.button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;

    /* Transition property */
    transition: all 0.3s ease;
    /* transition: background-color 0.3s ease, transform 0.2s ease; */
}

.button:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}</code></pre>
                    </div>

                    <h3>Keyframe Animations</h3>
                    <div class="code-example">
                        <pre><code>/* Define keyframes */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Apply animations */
.animated-element {
    animation: fadeInUp 0.6s ease-out;
}

.pulsing-element {
    animation: pulse 2s infinite;
}</code></pre>
                    </div>

                    <h2>Practice Project: Responsive Portfolio</h2>
                    <p>Let's style the HTML portfolio from Chapter 1 with modern CSS:</p>

                    <div class="project-box">
                        <h3>🎯 Project Goals</h3>
                        <ul>
                            <li>Create a modern, responsive design</li>
                            <li>Use flexbox for navigation and layout</li>
                            <li>Add hover effects and transitions</li>
                            <li>Implement mobile-first responsive design</li>
                            <li>Style typography and colors professionally</li>
                        </ul>
                    </div>

                    <div class="code-example">
                        <pre><code>/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Header */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 4rem 2rem;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    animation: fadeInUp 0.8s ease-out;
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* Navigation */
nav {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 1rem 0;
}

nav li {
    margin: 0 1rem;
}

nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

nav a:hover {
    background-color: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* Main content */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

section {
    background: white;
    margin: 2rem 0;
    padding: 3rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

section:hover {
    transform: translateY(-5px);
}

section h2 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
}

/* Profile image */
#about img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    float: left;
    margin: 0 2rem 1rem 0;
    border: 5px solid #667eea;
    transition: transform 0.3s ease;
}

#about img:hover {
    transform: scale(1.05);
}

/* Skills list */
#skills ul {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    list-style: none;
}

#skills li {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

#skills li:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-3px);
}

/* Project cards */
#projects article {
    background: #f8f9fa;
    padding: 2rem;
    margin: 1rem 0;
    border-radius: 8px;
    border-left: 5px solid #667eea;
    transition: all 0.3s ease;
}

#projects article:hover {
    background: white;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateX(10px);
}

#projects h3 {
    color: #333;
    margin-bottom: 1rem;
}

#projects a {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

#projects a:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Footer */
footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 3rem 2rem;
    margin-top: 3rem;
}

footer h2 {
    color: white;
    margin-bottom: 1rem;
}

footer a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #4facfe;
}

/* Responsive design */
@media (max-width: 768px) {
    header h1 {
        font-size: 2rem;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav li {
        margin: 0.5rem 0;
    }

    main {
        padding: 1rem;
    }

    section {
        padding: 2rem 1rem;
    }

    #about img {
        float: none;
        display: block;
        margin: 0 auto 2rem auto;
    }

    #skills ul {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}</code></pre>
                    </div>

                    <h2>Key Takeaways</h2>
                    <div class="highlight-box">
                        <h3>✅ What You've Learned</h3>
                        <ul>
                            <li>CSS controls the visual presentation of HTML</li>
                            <li>The box model is fundamental to layout understanding</li>
                            <li>Flexbox and Grid provide powerful layout options</li>
                            <li>Responsive design ensures compatibility across devices</li>
                            <li>Transitions and animations enhance user experience</li>
                            <li>Mobile-first approach is the modern standard</li>
                        </ul>
                    </div>

                    <h2>Next Steps</h2>
                    <p>With HTML structure and CSS styling mastered, you're ready to add interactivity with JavaScript. In the next chapter, we'll explore how to make your websites dynamic and responsive to user actions.</p>
                </div>
                <div class="page-footer">
                    <button class="nav-btn prev" onclick="showPage('chapter1')">← Chapter 1: HTML Fundamentals</button>
                    <button class="nav-btn next" onclick="showPage('chapter3')">Chapter 3: JavaScript Essentials →</button>
                </div>
            </div>
        </main>
    </div>

    <script src="scripts/ebook.js"></script>
</body>
</html>
