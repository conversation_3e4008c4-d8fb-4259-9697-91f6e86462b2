/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8fafc;
}

/* Ebook Container */
.ebook-container {
    display: flex;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

/* Navigation */
.ebook-nav {
    width: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.nav-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    background: inherit;
}

.nav-header h1 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.nav-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.nav-menu {
    list-style: none;
    padding: 1rem 0;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-menu a:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #4facfe;
    color: white;
}

.nav-menu a.active {
    background: rgba(255, 255, 255, 0.15);
    border-left-color: #4facfe;
    color: white;
}

/* Main Content */
.ebook-content {
    flex: 1;
    margin-left: 300px;
    background: white;
}

/* Page Styles */
.page {
    display: none;
    min-height: 100vh;
    padding: 3rem;
    animation: fadeIn 0.5s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Page Header */
.page-header {
    margin-bottom: 3rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 2rem;
}

.chapter-label {
    display: inline-block;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

/* Cover Page Styles */
.cover-page {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: calc(100vh - 6rem);
    text-align: center;
}

.cover-header {
    margin-top: 2rem;
}

.cover-category {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 2rem;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.cover-title {
    font-size: 4rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.cover-subtitle {
    font-size: 2.5rem;
    font-weight: 600;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
}

/* Feature Grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-item {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.feature-item p {
    color: #718096;
    font-size: 0.95rem;
}

/* Cover Footer */
.cover-footer {
    margin-top: auto;
    padding-top: 3rem;
}

.author-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.author-info p {
    color: #718096;
    font-size: 1.1rem;
}

.book-stats {
    margin-top: 2rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 30px;
    display: inline-block;
    font-weight: 500;
}

/* Table of Contents */
.toc-content {
    max-width: 800px;
}

.toc-section {
    margin-bottom: 3rem;
}

.toc-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
}

.toc-list {
    list-style: none;
}

.toc-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f7fafc;
}

.toc-list a {
    color: #4a5568;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.toc-list a:hover {
    color: #667eea;
}

.page-num {
    color: #a0aec0;
    font-weight: 400;
}

/* Content Styles */
.page-content {
    max-width: 800px;
    font-size: 1.1rem;
    line-height: 1.8;
}

.page-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
    margin: 2rem 0 1rem 0;
}

.page-content h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #4a5568;
    margin: 1.5rem 0 0.75rem 0;
}

.page-content p {
    margin-bottom: 1.5rem;
    color: #4a5568;
}

/* Highlight Box */
.highlight-box {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-left: 4px solid #0ea5e9;
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.highlight-box h3 {
    color: #0c4a6e;
    margin-bottom: 1rem;
}

.highlight-box ul {
    margin-left: 1.5rem;
}

.highlight-box li {
    margin-bottom: 0.5rem;
    color: #0c4a6e;
}

/* Audience Grid */
.audience-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.audience-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.audience-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.audience-card h3 {
    color: #2d3748;
    margin-bottom: 0.75rem;
    font-size: 1.2rem;
}

.audience-card p {
    color: #718096;
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Learning Path */
.learning-path {
    margin: 2rem 0;
}

.path-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.step-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.step-content h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.step-content p {
    color: #718096;
    margin-bottom: 0;
    font-size: 0.95rem;
}

/* Page Footer */
.page-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 4rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.nav-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.nav-btn.prev {
    background: linear-gradient(135deg, #718096, #4a5568);
}

/* Code Examples */
.code-example {
    background: #1a1a1a;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
    overflow-x: auto;
    border: 1px solid #333;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.code-example pre {
    margin: 0;
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    color: #f8f8f2;
}

.code-example code {
    font-family: 'Fira Code', monospace;
    background: none;
    padding: 0;
    border-radius: 0;
    color: #f8f8f2;
}

/* Syntax highlighting */
.code-example .keyword { color: #ff79c6; }
.code-example .string { color: #f1fa8c; }
.code-example .comment { color: #6272a4; }
.code-example .tag { color: #ff79c6; }
.code-example .attr-name { color: #50fa7b; }
.code-example .attr-value { color: #f1fa8c; }

/* Project Box */
.project-box {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.project-box h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.project-box ul {
    margin-left: 1.5rem;
}

.project-box li {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

/* Inline Code */
code {
    background: #f1f5f9;
    color: #e53e3e;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Fira Code', monospace;
    font-size: 0.9em;
    border: 1px solid #e2e8f0;
}

/* Lists in content */
.page-content ul {
    margin-left: 2rem;
    margin-bottom: 1.5rem;
}

.page-content ol {
    margin-left: 2rem;
    margin-bottom: 1.5rem;
}

.page-content li {
    margin-bottom: 0.5rem;
    color: #4a5568;
}

/* Strong and emphasis */
.page-content strong {
    font-weight: 600;
    color: #2d3748;
}

.page-content em {
    font-style: italic;
    color: #4a5568;
}

/* Blockquotes */
blockquote {
    border-left: 4px solid #667eea;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #4a5568;
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
}

tr:hover {
    background: #f8fafc;
}

/* Warning/Info boxes */
.warning-box {
    background: linear-gradient(135deg, #fed7d7, #feb2b2);
    border-left: 4px solid #e53e3e;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.warning-box h3 {
    color: #c53030;
    margin-bottom: 0.5rem;
}

.info-box {
    background: linear-gradient(135deg, #bee3f8, #90cdf4);
    border-left: 4px solid #3182ce;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.info-box h3 {
    color: #2c5282;
    margin-bottom: 0.5rem;
}

/* Success box */
.success-box {
    background: linear-gradient(135deg, #c6f6d5, #9ae6b4);
    border-left: 4px solid #38a169;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.success-box h3 {
    color: #276749;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ebook-nav {
        width: 100%;
        height: auto;
        position: relative;
    }

    .ebook-content {
        margin-left: 0;
    }

    .nav-toggle {
        display: block;
        float: right;
    }

    .nav-menu {
        display: none;
    }

    .nav-menu.active {
        display: block;
    }

    .page {
        padding: 2rem 1rem;
    }

    .cover-title {
        font-size: 2.5rem;
    }

    .cover-subtitle {
        font-size: 1.8rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .audience-grid {
        grid-template-columns: 1fr;
    }

    .page-footer {
        flex-direction: column;
        gap: 1rem;
    }

    .code-example {
        padding: 1rem;
        font-size: 0.8rem;
    }

    .code-example pre {
        font-size: 0.8rem;
    }
}
